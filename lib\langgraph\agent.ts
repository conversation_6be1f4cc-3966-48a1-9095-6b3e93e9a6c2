import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage } from "@langchain/core/messages";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { RunnableConfig } from "@langchain/core/runnables";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";
import { functions } from "@/lib/AI/functions-ai-chat";

// Define media item interface
export interface MediaItem {
    id: string;
    fileName: string;
    mimeType: string;
    url: string;
    filePath: string;
}

// Define the state for our agent
const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    input: Annotation<string>(),
    media: Annotation<MediaItem[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [],
    }),
    output: Annotation<string>(),
    personaId: Annotation<string>(),
});

// Define the schema for the agent's output
const recipeSchema = z.object({
    title: z.string().min(1).max(200),
    description: z.string(),
    // Add other fields as needed
});


export class ChatAgent {
    private systemPrompt = `You are a helpful financial assistant. You can analyze financial documents, receipts, statements, and answer questions about personal finance management.
    
    You have access to comprehensive financial tools that allow you to:
    - Fetch and view accounts, categories, transactions, and projects
    - Create new financial records (transactions, accounts, categories, projects)
    - Update existing financial data
    - Analyze spending patterns and financial data
    
    When users ask about their financial data, use the appropriate tools to fetch the information. When they want to create or modify financial records, use the creation and update tools.
    
    Important: All monetary amounts in the system use fixed-point arithmetic (multiplied by 1,000). The tools handle this conversion automatically, so you can work with normal decimal values.`;

    private financialTools: any[] = [];
    private toolNode: ToolNode<typeof AgentState.State> | null = null;
    private boundModel: ChatGoogleGenerativeAI | null = null;
    private currentPersonaId: string = "";

    constructor() {
        // Tools and model will be initialized with personaId context
    }

    private mimeFromExt(filePath: string): string {
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.png':
                return 'image/png';
            case '.gif':
                return 'image/gif';
            case '.webp':
                return 'image/webp';
            case '.pdf':
                return 'application/pdf';
            case '.mp4':
                return 'video/mp4';
            case '.mov':
                return 'video/quicktime';
            case '.webm':
                return 'video/webm';
            default:
                return 'application/octet-stream';
        }
    }

    private async prepareRequestNode(state: typeof AgentState.State) {
        const { input, media } = state;
        
        // Create the content array starting with the text input
        const content: Array<{
            type: string;
            text?: string;
            mime_type?: string;
            source_type?: string;
            data?: string;
        }> = [
            {
                type: "text",
                text: input,
            },
        ];

        // Add media files to the content
        for (const mediaItem of media) {
            try {
                // Read and encode the media file as base64
                const mediaData = fs.readFileSync(mediaItem.filePath);
                const base64Media = mediaData.toString('base64');
                
                content.push({
                    type: "file",
                    mime_type: mediaItem.mimeType,
                    source_type: "base64",
                    data: base64Media,
                });

                // Add a text description for the media
                content.push({
                    type: "text",
                    text: `Attached file: ${mediaItem.fileName} (${mediaItem.mimeType})`,
                });
            } catch (error) {
                console.error(`Error reading media file ${mediaItem.fileName}:`, error);
                // Continue processing other files
            }
        }

        const humanMessage = new HumanMessage({
            content: content,
        });

        return { messages: [new SystemMessage(this.systemPrompt), humanMessage] };
    }

    // Function to determine whether to continue or end
    private shouldContinue = (state: typeof AgentState.State) => {
        const { messages } = state;
        const lastMessage = messages[messages.length - 1] as AIMessage;
        
        // If there is no function call, then we finish
        if (!lastMessage.tool_calls || lastMessage.tool_calls.length === 0) {
            return END;
        }
        // Otherwise if there is, we continue to tools
        return "tools";
    };

    // Initialize tools and model for a specific personaId
    private initializeForPersona(personaId: string) {
        if (this.currentPersonaId !== personaId || !this.boundModel || !this.toolNode) {
            // Get API key from environment
            const apiKey = process.env.GOOGLE_API_KEY;
            if (!apiKey) {
                throw new Error('GOOGLE_API_KEY environment variable is not set');
            }

            // Create financial tools with personaId context
            this.financialTools = createFinancialTools(personaId);
            
            // Create tool node
            this.toolNode = new ToolNode<typeof AgentState.State>(this.financialTools);

            // Initialize the Gemini model
            const model = new ChatGoogleGenerativeAI({
                model: "gemini-2.0-flash-exp",
                temperature: 0.1,
                maxRetries: 2,
                apiKey: apiKey,
            });

            // Bind tools to the model
            this.boundModel = model.bindTools(this.financialTools);
            this.currentPersonaId = personaId;
        }
    }

    // Modified agent node to handle tool calling
    private async callModel(state: typeof AgentState.State, config?: RunnableConfig) {
        const { messages, personaId } = state;

        // Initialize tools and model for this persona
        this.initializeForPersona(personaId);

        // Limit message history to prevent context overflow
        let modelMessages = [];
        for (let i = messages.length - 1; i >= 0; i--) {
            modelMessages.push(messages[i]);
            if (modelMessages.length >= 10) {
                if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
                    break;
                }
            }
        }
        modelMessages.reverse();

        try {
            const response = await this.boundModel!.invoke(modelMessages, config);
            // Return an object because this will get added to the existing list
            return { messages: [response] };
        } catch (error) {
            console.error('Model invocation error:', error);
            const errorMessage = new AIMessage({
                content: "I apologize, but I encountered an error processing your request. Please try again."
            });
            return { messages: [errorMessage] };
        }
    }

    // Tool execution node
    private async executeTools(state: typeof AgentState.State) {
        const { personaId } = state;
        
        // Ensure tools are initialized for this persona
        this.initializeForPersona(personaId);
        
        // Execute tools using the initialized tool node
        return await this.toolNode!.invoke(state);
    }

    public async invoke(input: { messages: BaseMessage[], input: string, media?: MediaItem[], personaId: string }) {
        const workflow = new StateGraph(AgentState)
            .addNode("prepareRequest", this.prepareRequestNode.bind(this))
            .addNode("agent", this.callModel.bind(this))
            .addNode("tools", this.executeTools.bind(this))
            .addEdge(START, "prepareRequest")
            .addEdge("prepareRequest", "agent")
            .addConditionalEdges("agent", this.shouldContinue)
            .addEdge("tools", "agent");

        const agent = workflow.compile();
        const result = await agent.invoke({
            messages: input.messages || [],
            input: input.input,
            media: input.media || [],
            output: "",
            personaId: input.personaId,
        });
        return result;
    }
}
