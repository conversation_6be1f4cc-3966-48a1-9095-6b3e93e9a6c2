import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";

// Receipt item schema that matches detailsTransactions table
export const ReceiptItemSchema = z.object({
  name: z.string().nullable().describe("Item name/description"),
  quantity: z.number().nullable().describe("Quantity of the item"),
  unitPrice: z.number().nullable().describe("Price per unit (will be converted to miliunits)"),
  amount: z.number().describe("Total amount for this item (will be converted to miliunits)"),
  categoryId: z.string().nullable().describe("Category ID if determinable from item"),
  projectId: z.string().nullable().describe("Project ID if applicable"),
});

// Receipt transaction schema that matches transactions table
export const ReceiptTransactionSchema = z.object({
  amount: z.number().describe("Total transaction amount (will be converted to miliunits)"),
  payee: z.string().describe("Merchant/store name"),
  notes: z.string().nullable().describe("Additional notes about the transaction"),
  date: z.string().describe("Transaction date in YYYY-MM-DD format"),
  accountId: z.string().nullable().describe("Account ID - will need to be set by user"),
  categoryId: z.string().nullable().describe("Main category ID for the transaction"),
  projectId: z.string().nullable().describe("Project ID if applicable"),
  detailsTransactions: z.array(ReceiptItemSchema).describe("Individual items from the receipt"),
});

// Complete receipt processing response
export const ReceiptProcessingSchema = z.object({
  transactions: z.array(ReceiptTransactionSchema).describe("Extracted transactions from receipt(s)"),
  suggestedCategories: z.array(z.string()).describe("Suggested category names that might need to be created"),
  confidence: z.number().min(0).max(1).describe("Confidence level of the extraction (0-1)"),
  processingNotes: z.string().optional().describe("Notes about the processing or any issues"),
});

export type ReceiptItem = z.infer<typeof ReceiptItemSchema>;
export type ReceiptTransaction = z.infer<typeof ReceiptTransactionSchema>;
export type ReceiptProcessingResult = z.infer<typeof ReceiptProcessingSchema>;

export interface MediaItem {
  id: string;
  fileName: string;
  mimeType: string;
  url: string;
  filePath: string;
}

export class ReceiptProcessor {
  private model: ChatGoogleGenerativeAI;
  private modelWithSchema: any;

  constructor() {
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY environment variable is not set');
    }

    this.model = new ChatGoogleGenerativeAI({
      model: "gemini-2.0-flash-exp",
      temperature: 0.1,
      maxRetries: 2,
      apiKey: apiKey,
    });

    // Create model with structured output
    this.modelWithSchema = this.model.withStructuredOutput(ReceiptProcessingSchema);
  }

  private createSystemPrompt(): string {
    return `You are an expert receipt parser that extracts structured financial transaction data from receipt images.

CRITICAL RULES:
1. Extract ALL individual items from receipts with their exact names, quantities, unit prices, and amounts
2. Return amounts as decimal numbers (e.g., 12.50, not 1250) - the system will convert to miliunits
3. For each item, suggest categoryId as null (user will assign) but add category names to suggestedCategories
4. Be extremely careful with number parsing - verify amounts add up correctly
5. Use the merchant name as the "payee" field
6. Extract date in YYYY-MM-DD format
7. Set accountId and projectId as null - user will assign these
8. Include tax, tip, and fees as separate line items if present
9. If multiple receipts are detected, create separate transaction objects

IMPORTANT: The response MUST match the exact database schema structure. All monetary amounts will be automatically converted to integer miliunits by the system.

Focus on accuracy - it's better to be conservative than to guess incorrectly.`;
  }

  public async processReceipts(mediaItems: MediaItem[]): Promise<ReceiptProcessingResult> {
    try {
      // Create content for the model
      const content: Array<{
        type: string;
        text?: string;
        mime_type?: string;
        source_type?: string;
        data?: string;
      }> = [
        {
          type: "text",
          text: `Please extract all transaction details from these ${mediaItems.length} receipt image(s). Return structured data following the ReceiptProcessingSchema format.`,
        }
      ];

      // Add all media files to the content
      for (const mediaItem of mediaItems) {
        // Check if it's an image or PDF
        if (!mediaItem.mimeType.startsWith('image/') && mediaItem.mimeType !== 'application/pdf') {
          console.warn(`Skipping non-image/PDF file: ${mediaItem.fileName}`);
          continue;
        }

        // Read and encode the media file as base64
        const mediaData = fs.readFileSync(mediaItem.filePath);
        const base64Media = mediaData.toString('base64');
        
        content.push({
          type: "file",
          mime_type: mediaItem.mimeType,
          source_type: "base64",
          data: base64Media,
        });

        // Add a text description for the media
        content.push({
          type: "text",
          text: `Receipt image: ${mediaItem.fileName}`,
        });
      }

      const messages = [
        new SystemMessage(this.createSystemPrompt()),
        new HumanMessage({ content: content })
      ];

      // Use structured output to get properly formatted response
      const result = await this.modelWithSchema.invoke(messages);
      
      return result as ReceiptProcessingResult;

    } catch (error) {
      console.error('Error processing receipts:', error);
      
      // Return a fallback result
      return {
        transactions: [{
          amount: 0,
          payee: "Unknown Merchant",
          notes: `Receipt processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          date: new Date().toISOString().split('T')[0],
          accountId: null,
          categoryId: null,
          projectId: null,
          detailsTransactions: [{
            name: "Receipt item (processing failed)",
            quantity: null,
            unitPrice: null,
            amount: 0,
            categoryId: null,
            projectId: null,
          }]
        }],
        suggestedCategories: [],
        confidence: 0,
        processingNotes: "Processing failed - manual entry required"
      };
    }
  }

  public async processSingleReceipt(mediaItem: MediaItem): Promise<ReceiptProcessingResult> {
    return this.processReceipts([mediaItem]);
  }
}