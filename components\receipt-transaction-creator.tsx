"use client";

import React, { useState } from "react";
import { ReceiptUpload } from "./receipt-upload";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, convertAmountFormMiliunits } from "@/lib/utils";
import { useGetAccounts } from "@/features/accounts/api/use-get-accounts";
import { useGetCategories } from "@/features/categories/api/use-get-categories-all";
import { toast } from "sonner";
import { 
  Receipt, 
  DollarSign, 
  Calendar, 
  Store, 
  FileText, 
  CheckCircle2,
  Edit3,
  Save,
  X
} from "lucide-react";

interface ReceiptTransaction {
  amount: number;
  payee: string;
  notes: string | null;
  date: string;
  accountId: string | null;
  categoryId: string | null;
  projectId: string | null;
  detailsTransactions: Array<{
    name: string | null;
    quantity: number | null;
    unitPrice: number | null;
    amount: number;
    categoryId: string | null;
    projectId: string | null;
  }>;
}

interface ProcessedReceiptData {
  transactions: ReceiptTransaction[];
  suggestedCategories: string[];
  confidence: number;
  processingNotes?: string;
  userAccounts: Array<{ id: string; name: string }>;
  userCategories: Array<{ id: string; name: string }>;
}

export function ReceiptTransactionCreator() {
  const [processedData, setProcessedData] = useState<ProcessedReceiptData | null>(null);
  const [editingTransactions, setEditingTransactions] = useState<ReceiptTransaction[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  
  const { data: accounts } = useGetAccounts();
  const { data: categories } = useGetCategories();

  const handleReceiptsProcessed = (data: { data: ProcessedReceiptData }) => {
    setProcessedData(data.data);
    setEditingTransactions([...data.data.transactions]);
  };

  const updateTransaction = (index: number, updates: Partial<ReceiptTransaction>) => {
    setEditingTransactions(prev => 
      prev.map((transaction, i) => 
        i === index ? { ...transaction, ...updates } : transaction
      )
    );
  };

  const updateDetailTransaction = (
    transactionIndex: number, 
    detailIndex: number, 
    updates: Partial<ReceiptTransaction['detailsTransactions'][0]>
  ) => {
    setEditingTransactions(prev => 
      prev.map((transaction, i) => 
        i === transactionIndex
          ? {
              ...transaction,
              detailsTransactions: transaction.detailsTransactions.map((detail, di) =>
                di === detailIndex ? { ...detail, ...updates } : detail
              )
            }
          : transaction
      )
    );
  };

  const createTransactions = async () => {
    if (!editingTransactions.length) return;

    // Validate that all transactions have required fields
    const invalidTransactions = editingTransactions.filter(t => !t.accountId);
    if (invalidTransactions.length > 0) {
      toast.error("Please select an account for all transactions");
      return;
    }

    setIsCreating(true);

    try {
      const response = await fetch('/api/receipts/create-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactions: editingTransactions,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      toast.success(result.message || "Transactions created successfully!");
      
      // Clear the form
      setProcessedData(null);
      setEditingTransactions([]);

    } catch (error) {
      console.error('Transaction creation error:', error);
      toast.error('Failed to create transactions');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setProcessedData(null);
    setEditingTransactions([]);
  };

  if (processedData && editingTransactions.length > 0) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Review Extracted Transactions
              </span>
              <div className="flex items-center gap-2">
                <Badge variant={processedData.confidence > 0.8 ? "default" : "secondary"}>
                  {Math.round(processedData.confidence * 100)}% confidence
                </Badge>
                <Button onClick={resetForm} variant="outline" size="sm">
                  <X className="h-4 w-4 mr-2" />
                  Start Over
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              Review and edit the extracted transaction data before creating transactions.
              {processedData.processingNotes && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                  <strong>Note:</strong> {processedData.processingNotes}
                </div>
              )}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Transactions */}
        <div className="space-y-4">
          {editingTransactions.map((transaction, transactionIndex) => (
            <Card key={transactionIndex}>
              <CardHeader>
                <CardTitle className="text-lg">
                  Transaction {transactionIndex + 1}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Main transaction fields */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor={`merchant-${transactionIndex}`}>Merchant</Label>
                    <Input
                      id={`merchant-${transactionIndex}`}
                      value={transaction.payee}
                      onChange={(e) => updateTransaction(transactionIndex, { payee: e.target.value })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`amount-${transactionIndex}`}>Total Amount</Label>
                    <Input
                      id={`amount-${transactionIndex}`}
                      type="number"
                      step="0.01"
                      value={transaction.amount}
                      onChange={(e) => updateTransaction(transactionIndex, { amount: parseFloat(e.target.value) || 0 })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`date-${transactionIndex}`}>Date</Label>
                    <Input
                      id={`date-${transactionIndex}`}
                      type="date"
                      value={transaction.date}
                      onChange={(e) => updateTransaction(transactionIndex, { date: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`account-${transactionIndex}`}>Account *</Label>
                    <Select 
                      value={transaction.accountId || ""} 
                      onValueChange={(value) => updateTransaction(transactionIndex, { accountId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts?.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor={`category-${transactionIndex}`}>Category</Label>
                    <Select 
                      value={transaction.categoryId || ""} 
                      onValueChange={(value) => updateTransaction(transactionIndex, { categoryId: value || null })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {categories?.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor={`notes-${transactionIndex}`}>Notes</Label>
                  <Textarea
                    id={`notes-${transactionIndex}`}
                    value={transaction.notes || ""}
                    onChange={(e) => updateTransaction(transactionIndex, { notes: e.target.value || null })}
                    placeholder="Additional notes..."
                  />
                </div>

                {/* Detail transactions */}
                {transaction.detailsTransactions.length > 0 && (
                  <div>
                    <Separator />
                    <h4 className="font-medium mt-4 mb-2">Receipt Items</h4>
                    <div className="space-y-2">
                      {transaction.detailsTransactions.map((detail, detailIndex) => (
                        <div key={detailIndex} className="grid grid-cols-12 gap-2 items-center p-2 bg-gray-50 rounded">
                          <div className="col-span-4">
                            <Input
                              placeholder="Item name"
                              value={detail.name || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { name: e.target.value || null })}
                            />
                          </div>
                          <div className="col-span-2">
                            <Input
                              type="number"
                              placeholder="Qty"
                              value={detail.quantity || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { quantity: parseFloat(e.target.value) || null })}
                            />
                          </div>
                          <div className="col-span-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Unit Price"
                              value={detail.unitPrice || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { unitPrice: parseFloat(e.target.value) || null })}
                            />
                          </div>
                          <div className="col-span-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Amount"
                              value={detail.amount}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { amount: parseFloat(e.target.value) || 0 })}
                            />
                          </div>
                          <div className="col-span-2">
                            <Select 
                              value={detail.categoryId || ""} 
                              onValueChange={(value) => updateDetailTransaction(transactionIndex, detailIndex, { categoryId: value || null })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="">None</SelectItem>
                                {categories?.map((category) => (
                                  <SelectItem key={category.id} value={category.id}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Suggested categories */}
        {processedData.suggestedCategories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Suggested Categories</CardTitle>
              <CardDescription>
                Consider creating these categories if they don't exist:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {processedData.suggestedCategories.map((category, index) => (
                  <Badge key={index} variant="outline">
                    {category}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button onClick={resetForm} variant="outline">
            Cancel
          </Button>
          <Button onClick={createTransactions} disabled={isCreating}>
            {isCreating ? (
              <>Creating...</>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create {editingTransactions.length} Transaction{editingTransactions.length > 1 ? 's' : ''}
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ReceiptUpload
        onReceiptsProcessed={handleReceiptsProcessed}
        onError={(error) => toast.error(error)}
        maxFiles={5}
      />
      
      {/* Help text */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-gray-600">
          <p>1. <strong>Upload receipts:</strong> Take photos or upload images/PDFs of your receipts</p>
          <p>2. <strong>AI processing:</strong> Our AI will extract transaction details and itemized lists</p>
          <p>3. <strong>Review & edit:</strong> Check and modify the extracted data as needed</p>
          <p>4. <strong>Assign accounts:</strong> Select which account each transaction should be assigned to</p>
          <p>5. <strong>Create transactions:</strong> Save the transactions to your financial records</p>
        </CardContent>
      </Card>
    </div>
  );
}